import { ToolUse } from '../../types/message';
import { <PERSON>lHelpers, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1 } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * Research工具处理器
 * // TODO: 目前 Research 工具名不副实，后面要把这部分丰富了
 * 用于执行研究任务，包括信息收集、分析和总结
 */
export class ResearchHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;
    const stage: string | undefined = block.params.stage;

    try {
      if (block.partial) {
        return;
      } else {
        if (!stage) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('research', 'stage', this.context),
            this.context.stateManager
          );
          return;
        }
        if(!['start', 'end'].includes(stage)) {
            this.context.stateManager.updateState({
              consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
            });
            ToolHelpers.pushToolResult(
              block,
              userMessageContent,
              `The stage value is not valid. the stage must be one of the following values: ['start', 'end'] \n`,
              this.context.stateManager
            );
            return;
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'research'
        });

        const toolLog = ToolHelpers.generateToolLog('research', this.context.loggerManager);
        toolLog.start('');
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {},
          metadata: {
            name: block.name
          }
        });

        toolLog.end('');

        generationCall?.end({});
        if(stage === 'start'){
            await this.context.messageService.say('research_start', '', false);
        }

        if(stage === 'end'){
          // TODO: 判断research 过程中有没有生成 requirement 和 design 文档
            await this.context.messageService.say('research_end', '', false);
            this.context.agentManager.handleCompletedTask();
        }
        ToolHelpers.pushToolResult(
          block,
          userMessageContent,
          `The research has been ${stage === 'start'? 'started' : 'ended'} successfully`,
          this.context.stateManager
        );

        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'research',
        error,
        'research' as any
      );
    }
  }
}